'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Trash2, Loader2, AlertCircle, UserX } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface DeleteUserDialogProps {
  userId: string;
  userName: string;
  onSuccess?: () => void;
}

// Helper function to get user initials
function getUserInitials(name: string): string {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
}

export default function DeleteUserDialog({ userId, userName, onSuccess }: DeleteUserDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  const handleDelete = async () => {
    if (!userId) return;

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({
          error: 'Unknown error',
          type: 'network',
          shouldRetry: response.status >= 500
        }));

        // Enhanced error message based on status code
        let errorMessage = errorData.error || `Failed to delete user (${response.status})`;

        if (response.status === 408) {
          errorMessage = 'Operation timed out. Please try again.';
        } else if (response.status === 503) {
          errorMessage = 'Service temporarily unavailable. Please try again in a moment.';
        } else if (response.status === 401) {
          errorMessage = 'Authentication failed. Please refresh the page and try again.';
        } else if (response.status === 403) {
          errorMessage = errorData.error || 'You do not have permission to delete this user.';
        }

        throw new Error(errorMessage);
      }

      toast({
        title: 'User Deleted',
        description: `${userName} has been successfully deleted.`,
        variant: 'default',
      });

      // Close the dialog
      setIsOpen(false);

      // Call the success callback or navigate back
      if (onSuccess) {
        onSuccess();
      } else {
        router.push('/admin/users');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete user',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button
          variant="destructive"
          className="flex items-center gap-2 px-4 py-2"
        >
          <Trash2 className="h-4 w-4" />
          Delete User
        </Button>
      </AlertDialogTrigger>

      <AlertDialogContent className="bg-vista-dark border-vista-dark-lighter max-w-md w-full p-0 overflow-hidden">
        <div className="bg-red-600/10 p-6 flex flex-col items-center justify-center border-b border-vista-dark-lighter">
          <div className="bg-red-600/20 p-3 rounded-full mb-4">
            <UserX className="h-8 w-8 text-red-500" />
          </div>
          <AlertDialogHeader className="text-center space-y-1">
            <AlertDialogTitle className="text-xl text-vista-light">Delete User Account</AlertDialogTitle>
            <div className="flex items-center justify-center gap-2 mt-2">
              <Avatar className="h-10 w-10 border-2 border-vista-dark-lighter">
                <AvatarFallback className="bg-vista-blue text-white">
                  {getUserInitials(userName)}
                </AvatarFallback>
              </Avatar>
              <span className="font-semibold text-vista-light">{userName}</span>
            </div>
          </AlertDialogHeader>
        </div>

        <div className="p-6">
          <div className="text-vista-light/80 mb-6">
            <AlertDialogDescription className="block mb-4">
              You are about to permanently delete this user account and all associated data. This action <span className="text-red-500 font-semibold">cannot be undone</span>.
            </AlertDialogDescription>

            <div className="flex items-start gap-2 bg-yellow-500/10 p-3 rounded-md border border-yellow-500/20">
              <AlertCircle className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-vista-light/90">
                This will remove all user data including profiles, watch history, preferences, and activity logs.
              </div>
            </div>
          </div>

          <AlertDialogFooter className="flex flex-col sm:flex-row gap-3 sm:gap-2 mt-6">
            <AlertDialogCancel
              className="bg-vista-dark-lighter text-vista-light hover:bg-vista-dark-lighter/80 hover:text-vista-light w-full sm:w-auto"
              disabled={isDeleting}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 text-white hover:bg-red-700 focus:ring-red-600 w-full sm:w-auto"
              onClick={(e) => {
                e.preventDefault();
                handleDelete();
              }}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete User Account'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}
