'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ArrowLeft, X, Film } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import VidSrcPlayer from '@/components/VidSrcPlayer';
import { Navbar } from '@/components/Navbar';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import PersonImage from '@/components/PersonImage';
import { Star, Users, Calendar, Clock } from 'lucide-react';
import EpisodeList from '@/components/EpisodeList';
import WatchActions from '@/components/WatchActions';
import { CastCrewCard } from '@/components/cast-crew/CastCrewCard';
import { getTVShowDetails, getTVSeasonDetails } from '@/lib/tmdb-api';
import { Episode } from '@/types/index';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import RelatedContent from '@/components/RelatedContent';
import { ContentCardType } from '@/lib/content-utils';
import DetailBanner from '@/components/DetailBanner';
import { CastCrewSection } from '@/components/cast-crew/CastCrewSection';
import { motion, AnimatePresence } from 'framer-motion';
import { extractTrailerFromTMDB } from '@/lib/trailer-utils';
import dynamic from 'next/dynamic';

// Dynamically import CustomVideoPlayer for client-side only
const CustomVideoPlayer = dynamic(() => import('@/components/CustomVideoPlayer'), {
  ssr: false,
  loading: () => (
    <div className="w-full aspect-video bg-vista-dark-lighter rounded-lg overflow-hidden flex items-center justify-center">
      <div className="flex flex-col items-center space-y-4">
        <div className="w-8 h-8 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin"></div>
        <p className="text-vista-light/70 text-sm">Loading video player...</p>
      </div>
    </div>
  )
});

interface DirectWatchClientProps {
  contentId: string;
}

interface CastMember {
  id: string;
  name: string;
  character: string;
  profile_path: string | null;
  order: number;
  gender?: number;
  popularity?: number;
}

interface CrewMember {
  id: string;
  name: string;
  job: string;
  department: string;
  profile_path: string | null;
  gender?: number;
  popularity?: number;
}

interface Credit {
  id: number;
  name: string;
  character?: string;
  job?: string;
  department?: string;
  profile_path: string | null;
  order?: number;
  gender?: number;
  popularity?: number;
}

interface ContentData {
  id: string;
  title: string;
  type: 'movie' | 'show';
  imdbId?: string;
  tmdbId?: string;
  poster?: string;
  year?: string;
  description?: string;
  rating?: number;
  duration?: string;
  genre?: string[];
  director?: string;
  season?: number;
  episode?: number;
  posterPath?: string;
  backdropPath?: string;
  releaseYear?: number;
  ageRating?: string;
  creators?: string;
  starring?: string[];
  genres?: string[];
  seasons?: number;
  imagePath?: string;
  bannerImage?: string;
  awards?: string;
  rated?: string;
  released?: string;
  metascore?: number;
  views?: number;
  trailerVideoId?: string;
  videos?: unknown;
  similar?: ContentCardType[];
  recommendations?: ContentCardType[];
  cast?: CastMember[];
  crew?: CrewMember[];
  credits?: {
    cast: CastMember[];
    crew: CrewMember[];
  };
}

interface TMDbEpisode {
  id: number;
  name: string;
  overview: string;
  still_path: string | null;
  episode_number: number;
  season_number: number;
  air_date: string;
  runtime: number;
}

export default function DirectWatchClient({ contentId }: DirectWatchClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const contentType = searchParams?.get('contentType') as 'movie' | 'show' | null;
  const seasonParam = searchParams?.get('season');
  const episodeParam = searchParams?.get('episode');
  const season = seasonParam ? parseInt(seasonParam) : undefined;
  const episode = episodeParam ? parseInt(episodeParam) : undefined;
  const title = searchParams?.get('title') || 'Unknown Title';
  const [content, setContent] = useState<ContentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [seasons, setSeasons] = useState<{ seasonNumber: number; episodes: Episode[] }[]>([]);
  const seasonsRef = useRef(seasons);
  seasonsRef.current = seasons;
  const [seasonCount, setSeasonCount] = useState(0);
  const [isLoadingSeasons, setIsLoadingSeasons] = useState(false);
  const fetchingSeasonsRef = useRef<Set<number>>(new Set());
  const [activeSeason, setActiveSeason] = useState<number | undefined>(season);
  const [activeEpisode, setActiveEpisode] = useState<number | undefined>(episode);
  
  // Trailer state
  const [trailerPlaying, setTrailerPlaying] = useState(false);
  const [trailerVideoId, setTrailerVideoId] = useState<string | undefined>(undefined);

  useEffect(() => {
    const fetchContent = async () => {
      try {
        setLoading(true);
        const apiContentType = contentType === 'show' ? 'show' : 'movie';
        const response = await fetch(`/api/content?id=${contentId}&type=${apiContentType}`);
        if (!response.ok) throw new Error(`Failed to fetch content details: ${response.status}`);
        const contentData = await response.json();

        let castData: CastMember[] = [];
        let crewData: CrewMember[] = [];

        if (contentData.credits) {
          castData = contentData.credits.cast?.slice(0, 20).map((cast: Credit) => ({
            id: cast.id.toString(), name: cast.name, character: cast.character || 'Unknown Role',
            profile_path: cast.profile_path, order: cast.order, gender: cast.gender, popularity: cast.popularity
          })) || [];
          crewData = contentData.credits.crew?.filter((crew: Credit) =>
            ['Director', 'Writer', 'Producer', 'Executive Producer'].includes(crew.job || '') ||
            ['Directing', 'Writing'].includes(crew.department || '')
          ).slice(0, 10).map((crew: Credit) => ({
            id: crew.id.toString(), name: crew.name, job: crew.job || '', department: crew.department || '',
            profile_path: crew.profile_path, gender: crew.gender, popularity: crew.popularity
          })) || [];
        }
        
        const recommendations = (contentData.recommendations?.results || []).map((item: Credit & { media_type: 'movie' | 'show', release_date: string, adult: boolean, poster_path: string, title: string }) => ({
            id: item.id.toString(), title: item.title, imagePath: item.poster_path ? `https://image.tmdb.org/t/p/w500${item.poster_path}` : '/images/placeholder-poster.jpg',
            type: item.media_type || 'movie', year: item.release_date?.substring(0, 4) || '', ageRating: item.adult ? '18+' : 'PG'
        }));

        const similar = (contentData.similar?.results || []).map((item: Credit & { media_type: 'movie' | 'show', release_date: string, adult: boolean, poster_path: string, title: string }) => ({
            id: item.id.toString(), title: item.title, imagePath: item.poster_path ? `https://image.tmdb.org/t/p/w500${item.poster_path}` : '/images/placeholder-poster.jpg',
            type: item.media_type || 'movie', year: item.release_date?.substring(0, 4) || '', ageRating: item.adult ? '18+' : 'PG'
        }));

        const enhancedContent: ContentData = {
          id: contentData.id.toString(), title: contentData.title, type: contentType === 'show' ? 'show' : 'movie',
          description: contentData.overview || '', releaseYear: contentData.year ? parseInt(contentData.year) : 0,
          ageRating: contentData.rated || (contentType === 'movie' ? 'PG-13' : 'TV-14'),
          creators: contentData.director || contentData.creator || '', starring: contentData.actors || [],
          genres: contentData.genres || [], seasons: contentData.seasons || 1,
          imagePath: contentData.posterPath || '', bannerImage: contentData.backdropPath || contentData.posterPath || '',
          duration: contentData.runtime ? `${contentData.runtime} min` : '45-60 min',
          imdbId: contentData.imdbId || '', tmdbId: contentData.tmdbId || '', director: contentData.director || '',
          awards: contentData.awards || '', rated: contentData.rated || '', released: contentData.released || '',
          metascore: contentData.metascore || 0, rating: contentData.rating || undefined,
          views: contentData.views || undefined, videos: contentData.videos,
          similar, recommendations,
          cast: castData, crew: crewData, credits: contentData.credits,
          poster: contentData.posterPath || '/images/placeholder-poster.jpg', year: contentData.year?.toString() || '2024',
          genre: contentData.genres || [], season, episode
        };
        setContent(enhancedContent);
        
        // Extract trailer video ID from videos
        if (contentData.videos) {
          const extractedTrailerId = extractTrailerFromTMDB(contentData.videos);
          setTrailerVideoId(extractedTrailerId);
          console.log('Trailer extraction results:', {
            hasVideos: !!contentData.videos,
            videoCount: contentData.videos?.results?.length || 0,
            trailerVideoId: extractedTrailerId,
            contentTitle: contentData.title
          });
        }
      } catch (err) {
        console.error('Error fetching content:', err);
        setError('Failed to load content');
      } finally {
        setLoading(false);
      }
    };
    fetchContent();
  }, [contentId, contentType, season, episode, title]);

  useEffect(() => {
    if (!content || content.type !== 'show' || !content.tmdbId) return;
    setIsLoadingSeasons(true);
    (async () => {
      try {
        const tmdbId = content.tmdbId ?? '';
        console.log('content.tmdbId:', content.tmdbId);
        const showDetails = await getTVShowDetails(tmdbId);
        setSeasonCount(showDetails.number_of_seasons || 0);
        const initialSeasonToFetch = activeSeason || 1;
        const seasonData = await getTVSeasonDetails(tmdbId, initialSeasonToFetch);
        const formattedSeason = {
          seasonNumber: initialSeasonToFetch,
          episodes: (seasonData.episodes as TMDbEpisode[]).map((ep: TMDbEpisode) => ({
            id: `${ep.id}`, title: ep.name, episodeNumber: ep.episode_number, seasonNumber: ep.season_number,
            description: ep.overview, thumbnail: ep.still_path ? `https://image.tmdb.org/t/p/w780${ep.still_path}` : '',
            runtime: ep.runtime || 0, airDate: ep.air_date
          }))
        };
        setSeasons([formattedSeason]);
        if (!activeSeason) {
          setActiveSeason(1);
        }
        if (!activeEpisode) {
          setActiveEpisode(1);
        }
      } catch (err) { console.error(err) } finally { setIsLoadingSeasons(false); }
    })();
  }, [content, activeSeason, activeEpisode]);

  const handleSeasonChange = useCallback(async (event: CustomEvent<{ season: number }>) => {
    const { season } = event.detail;
    if (!content?.tmdbId || !season) return;

    const isSeasonLoaded = seasonsRef.current.some(s => s.seasonNumber === season);
    if (isSeasonLoaded) {
      setActiveSeason(season);
      setActiveEpisode(1);
      return;
    }

    if (fetchingSeasonsRef.current.has(season)) {
      return; // Already fetching this season
    }
    
    setActiveSeason(season);
    setActiveEpisode(1);
    
    const tmdbId = content.tmdbId ?? '';
    setIsLoadingSeasons(true);
    fetchingSeasonsRef.current.add(season);

    try {
      const seasonData = await getTVSeasonDetails(tmdbId, season);
      const formattedSeason = {
        seasonNumber: season,
        episodes: (seasonData.episodes as TMDbEpisode[]).map((ep: TMDbEpisode) => ({
          id: `${ep.id}`,
          title: ep.name,
          episodeNumber: ep.episode_number,
          seasonNumber: ep.season_number,
          description: ep.overview,
          thumbnail: ep.still_path ? `https://image.tmdb.org/t/p/w780${ep.still_path}` : '',
          runtime: ep.runtime || 0,
          airDate: ep.air_date
        }))
      };
      setSeasons(prev => {
        const filtered = prev.filter(s => s.seasonNumber !== season);
        const updated = [...filtered, formattedSeason];
        return updated.sort((a, b) => a.seasonNumber - b.seasonNumber);
      });
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoadingSeasons(false);
      fetchingSeasonsRef.current.delete(season);
    }
  }, [content?.tmdbId]);

  const handleEpisodeChange = useCallback((event: CustomEvent<{ season: number; episode: number }>) => {
    const { season, episode } = event.detail;
    console.log(`DirectWatchClient: Episode change event received - Season ${season}, Episode ${episode}`);
    setActiveSeason(season);
    setActiveEpisode(episode);
    const url = new URL(window.location.href);
    url.searchParams.set('season', season.toString());
    url.searchParams.set('episode', episode.toString());
    window.history.replaceState({}, '', url.toString());
  }, []);

  useEffect(() => {
    const listener = (event: Event) => handleSeasonChange(event as CustomEvent<{ season: number }>);
    window.addEventListener('seasonChange', listener);
    return () => window.removeEventListener('seasonChange', listener);
  }, [handleSeasonChange]);

  useEffect(() => {
    const listener = (event: Event) => handleEpisodeChange(event as CustomEvent<{ season: number; episode: number }>);
    window.addEventListener('episodeChange', listener);
    return () => window.removeEventListener('episodeChange', listener);
  }, [handleEpisodeChange]);

  // Trailer handlers
  const handlePlayTrailer = () => {
    console.log('Trailer play requested:', {
      hasTrailer: !!trailerVideoId,
      trailerId: trailerVideoId,
      title: content?.title
    });
    
    if (trailerVideoId) {
      setTrailerPlaying(true);
      console.log(`[Analytics] User played trailer for: ${content?.title}`);
    } else {
      toast.info('No trailer available for this content');
    }
  };

  const handleCloseTrailer = () => {
    setTrailerPlaying(false);
  };

  const handleAddToList = () => {
    // TODO: Implement add to watchlist functionality
    toast.info('Add to list functionality coming soon!');
  };

  const handleShare = () => {
    // TODO: Implement share functionality
    toast.info('Share functionality coming soon!');
  };

  if (loading) return <div className="min-h-screen bg-vista-dark flex items-center justify-center"><div className="text-vista-light">Loading...</div></div>;
  if (error || !content) return <div className="min-h-screen bg-vista-dark flex items-center justify-center"><div className="text-red-500">{error || 'Content not found'}</div></div>;

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Video Player - Full Width */}
      <div className="w-full bg-black flex justify-center items-center">
        <div className="w-full max-w-7xl aspect-video">
          <VidSrcPlayer
            key={`${content.tmdbId}-${activeSeason ?? content.season}-${activeEpisode ?? content.episode}`}
            imdbId={String(content.imdbId ?? '')} 
            tmdbId={String(content.tmdbId ?? '')} 
            type={content.type === 'show' ? 'show' : 'movie'}
            season={typeof (activeSeason ?? content.season) === 'number' ? (activeSeason ?? content.season) : 1}
            episode={typeof (activeEpisode ?? content.episode) === 'number' ? (activeEpisode ?? content.episode) : 1}
            className="w-full h-full" 
            autoplay={true}
          />
        </div>
      </div>

      {/* Content Information Banner - Contained */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <DetailBanner
          title={content.title}
          description={content.description || ''}
          year={content.releaseYear || 0}
          ageRating={content.ageRating || ''}
          duration={content.duration || ''}
          genres={content.genres || []}
          starring={content.starring || []}
          creator={content.creators || ''}
          imagePath={content.bannerImage || content.imagePath || ''}
          director={content.director}
          awards={content.awards}
          rated={content.rated}
          released={content.released}
          metascore={content.metascore}
          trailerVideoId={trailerVideoId}
          onPlayTrailer={handlePlayTrailer}
          onAddToList={handleAddToList}
          onShare={handleShare}
        />
      </div>

      {/* Episodes Section - Full Screen Width */}
      {content.type === 'show' && (
        <div className="w-full bg-vista-dark py-8">
          <EpisodeList
            seasons={seasons}
            showTitle={content.title}
            seasonCount={seasonCount}
            isLoading={isLoadingSeasons}
            imdbId={content.imdbId ?? ''}
            tmdbId={content.tmdbId ?? ''}
            contentId={content.id}
            contentType="show"
            activeSeason={activeSeason}
            activeEpisode={activeEpisode}
          />
        </div>
      )}

      {/* Cast & Crew and Related Content - Back to Container */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-8">
        {/* Cast & Crew Section */}
        {content.cast && content.cast.length > 0 && (
          <CastCrewSection cast={content.cast} crew={content.crew ?? []} />
        )}

        {/* Related Content Section */}
        <RelatedContent 
          title="More Like This" 
          contents={content.recommendations?.length ? content.recommendations : content.similar || []} 
        />
      </div>

      {/* Trailer Video Player Modal */}
      <AnimatePresence>
        {trailerPlaying && trailerVideoId && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4 md:p-8"
            onClick={handleCloseTrailer} // Close on overlay click
          >
            {/* Close button */}
            <button
              className="absolute top-2 right-2 sm:top-4 sm:right-4 text-white/70 hover:text-white z-[60] p-2 rounded-full hover:bg-white/10 transition-all duration-200"
              onClick={(e) => {
                e.stopPropagation(); // Prevent overlay click when clicking button
                handleCloseTrailer();
              }}
              aria-label="Close trailer"
            >
              <X className="w-6 h-6 sm:w-8 sm:h-8" />
            </button>

            {/* Video Player */}
            <div
              className="relative w-full h-full max-w-7xl max-h-[90vh] bg-black rounded-none sm:rounded-lg overflow-hidden shadow-2xl"
              onClick={(e) => e.stopPropagation()} // Prevent closing when clicking player
              style={{ aspectRatio: '16/9' }}
            >
              <CustomVideoPlayer videoId={trailerVideoId} autoPlay={true} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
