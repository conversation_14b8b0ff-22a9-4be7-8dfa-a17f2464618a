/**
 * User Deletion Fixes Validation Script
 * 
 * This script validates that all the fixes for user deletion have been properly implemented.
 */

const fs = require('fs');
const path = require('path');

/**
 * Check if a file exists and contains specific patterns
 */
function checkFileForPatterns(filePath, patterns, description) {
  console.log(`\n🔍 Checking ${description}...`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ File not found: ${filePath}`);
    return false;
  }
  
  console.log(`✅ File exists: ${path.basename(filePath)}`);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let allPatternsFound = true;
    
    for (const pattern of patterns) {
      if (pattern.regex.test(content)) {
        console.log(`✅ ${pattern.name}: Found`);
      } else {
        console.log(`❌ ${pattern.name}: Missing`);
        allPatternsFound = false;
      }
    }
    
    return allPatternsFound;
  } catch (error) {
    console.log(`❌ Error reading file: ${error.message}`);
    return false;
  }
}

/**
 * Validate admin authentication fixes
 */
function validateAdminAuthFixes() {
  const filePath = path.join(process.cwd(), 'src', 'lib', 'admin-auth.ts');
  const patterns = [
    {
      name: 'Enhanced error logging',
      regex: /console\.error\(['"]verifyAdmin: Production error details['"]/
    },
    {
      name: 'Error context logging',
      regex: /errorMessage.*error instanceof Error.*error\.message/
    },
    {
      name: 'MongoDB URI logging',
      regex: /mongodbUri.*MONGODB_URI.*defined.*undefined/
    }
  ];
  
  return checkFileForPatterns(filePath, patterns, 'Admin Authentication Fixes');
}

/**
 * Validate MongoDB connection fixes
 */
function validateMongoDBFixes() {
  const filePath = path.join(process.cwd(), 'src', 'lib', 'mongodb.ts');
  const patterns = [
    {
      name: 'Increased serverSelectionTimeoutMS',
      regex: /serverSelectionTimeoutMS:\s*isNetlify\s*\?\s*15000/
    },
    {
      name: 'Increased socketTimeoutMS',
      regex: /socketTimeoutMS:\s*isNetlify\s*\?\s*30000/
    },
    {
      name: 'Increased connectTimeoutMS',
      regex: /connectTimeoutMS:\s*isNetlify\s*\?\s*15000/
    },
    {
      name: 'Added maxIdleTimeMS',
      regex: /maxIdleTimeMS:/
    },
    {
      name: 'Added waitQueueTimeoutMS',
      regex: /waitQueueTimeoutMS:/
    }
  ];
  
  return checkFileForPatterns(filePath, patterns, 'MongoDB Connection Fixes');
}

/**
 * Validate user deletion route fixes
 */
function validateUserDeletionRouteFixes() {
  const filePath = path.join(process.cwd(), 'src', 'app', 'api', 'admin', 'users', '[id]', 'route.ts');
  const patterns = [
    {
      name: 'Enhanced error handling import',
      regex: /import.*createErrorResponse.*withTimeout.*generateRequestId.*from.*error-handler/
    },
    {
      name: 'Environment validation import',
      regex: /import.*validateEnvironmentVariables.*from.*env-validation/
    },
    {
      name: 'Request ID generation',
      regex: /generateRequestId\(\)/
    },
    {
      name: 'Environment validation call',
      regex: /validateEnvironmentVariables\(/
    },
    {
      name: 'withTimeout usage',
      regex: /withTimeout\(/
    },
    {
      name: 'Enhanced error response',
      regex: /createErrorResponse\(/
    },
    {
      name: 'OperationalError usage',
      regex: /OperationalError\(/
    }
  ];
  
  return checkFileForPatterns(filePath, patterns, 'User Deletion Route Fixes');
}

/**
 * Validate Netlify configuration fixes
 */
function validateNetlifyConfigFixes() {
  const filePath = path.join(process.cwd(), 'netlify.toml');
  const patterns = [
    {
      name: 'Increased admin function timeout to 60s',
      regex: /\[functions\."api\/admin\/\*"\][\s\S]*FUNCTION_TIME_LIMIT\s*=\s*"60"/
    },
    {
      name: 'User-specific endpoint configuration',
      regex: /\[functions\."api\/admin\/users\/\*"\]/
    },
    {
      name: 'User endpoint timeout 90s',
      regex: /\[functions\."api\/admin\/users\/\*"\][\s\S]*FUNCTION_TIME_LIMIT\s*=\s*"90"/
    },
    {
      name: 'Enhanced MongoDB settings',
      regex: /MONGODB_CONNECTION_LIMIT.*MONGODB_SERVERLESS_MODE.*MONGODB_RETRY_WRITES/
    }
  ];
  
  return checkFileForPatterns(filePath, patterns, 'Netlify Configuration Fixes');
}

/**
 * Validate frontend error handling fixes
 */
function validateFrontendFixes() {
  const filePath = path.join(process.cwd(), 'src', 'components', 'admin', 'DeleteUserDialog.tsx');
  const patterns = [
    {
      name: 'Enhanced error handling',
      regex: /response\.status === 408/
    },
    {
      name: 'Service unavailable handling',
      regex: /response\.status === 503/
    },
    {
      name: 'Authentication error handling',
      regex: /response\.status === 401/
    },
    {
      name: 'Forbidden error handling',
      regex: /response\.status === 403/
    }
  ];
  
  return checkFileForPatterns(filePath, patterns, 'Frontend Error Handling Fixes');
}

/**
 * Validate utility files exist
 */
function validateUtilityFiles() {
  console.log('\n🔍 Checking utility files...');
  
  const files = [
    {
      path: path.join(process.cwd(), 'src', 'lib', 'env-validation.ts'),
      name: 'Environment Validation Utility'
    },
    {
      path: path.join(process.cwd(), 'src', 'lib', 'error-handler.ts'),
      name: 'Error Handler Utility'
    }
  ];
  
  let allFilesExist = true;
  
  for (const file of files) {
    if (fs.existsSync(file.path)) {
      console.log(`✅ ${file.name}: Exists`);
    } else {
      console.log(`❌ ${file.name}: Missing`);
      allFilesExist = false;
    }
  }
  
  return allFilesExist;
}

/**
 * Main validation function
 */
function runValidation() {
  console.log('🚀 Validating User Deletion System Fixes\n');
  console.log('=' .repeat(50));
  
  const validations = [
    { name: 'Admin Authentication Fixes', fn: validateAdminAuthFixes },
    { name: 'MongoDB Connection Fixes', fn: validateMongoDBFixes },
    { name: 'User Deletion Route Fixes', fn: validateUserDeletionRouteFixes },
    { name: 'Netlify Configuration Fixes', fn: validateNetlifyConfigFixes },
    { name: 'Frontend Error Handling Fixes', fn: validateFrontendFixes },
    { name: 'Utility Files', fn: validateUtilityFiles }
  ];

  let passedValidations = 0;
  
  for (const validation of validations) {
    try {
      const result = validation.fn();
      if (result) {
        console.log(`\n✅ ${validation.name}: PASSED`);
        passedValidations++;
      } else {
        console.log(`\n❌ ${validation.name}: FAILED`);
      }
    } catch (error) {
      console.log(`\n❌ ${validation.name}: ERROR - ${error.message}`);
    }
  }

  console.log('\n' + '=' .repeat(50));
  console.log(`📊 Validation Results: ${passedValidations}/${validations.length} validations passed`);
  
  if (passedValidations === validations.length) {
    console.log('\n🎉 All validations passed! The user deletion fixes have been properly implemented.');
    console.log('\n📋 Summary of fixes applied:');
    console.log('   • Enhanced admin authentication with better error logging');
    console.log('   • Improved MongoDB connection timeouts for Netlify');
    console.log('   • Added comprehensive error handling with timeout management');
    console.log('   • Increased Netlify function timeouts (60s admin, 90s user ops)');
    console.log('   • Enhanced frontend error handling with specific status codes');
    console.log('   • Added environment validation and error categorization utilities');
  } else {
    console.log('\n⚠️ Some validations failed. Please review the issues above.');
  }
  
  console.log('\n🔧 Next steps:');
  console.log('   1. Deploy these changes to Netlify');
  console.log('   2. Test user deletion in production environment');
  console.log('   3. Monitor logs for any remaining issues');
  console.log('   4. Verify that environment variables are set in Netlify dashboard');
}

// Run the validation
runValidation();
