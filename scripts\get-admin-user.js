const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

async function getAdminUser() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Define User schema
    const UserSchema = new mongoose.Schema({
      name: String,
      email: String,
      role: String
    });

    const User = mongoose.models.User || mongoose.model('User', UserSchema);

    // Find an admin user
    const adminUser = await User.findOne({ role: 'admin' });
    
    if (adminUser) {
      console.log('Admin user found:');
      console.log('ID:', adminUser._id.toString());
      console.log('Name:', adminUser.name);
      console.log('Email:', adminUser.email);
    } else {
      console.log('No admin user found');
      
      // Find any user
      const anyUser = await User.findOne({});
      if (anyUser) {
        console.log('Regular user found:');
        console.log('ID:', anyUser._id.toString());
        console.log('Name:', anyUser.name);
        console.log('Email:', anyUser.email);
        console.log('Role:', anyUser.role);
      } else {
        console.log('No users found in database');
      }
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
  }
}

getAdminUser();
