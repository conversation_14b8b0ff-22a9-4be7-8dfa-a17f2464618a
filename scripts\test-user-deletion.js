/**
 * User Deletion Test Script
 * 
 * This script tests the user deletion functionality to ensure it works
 * correctly in both local and production environments.
 */

const { MongoClient } = require('mongodb');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_DB = process.env.MONGODB_DB || 'streamvista';

if (!MONGODB_URI) {
  console.error('Error: MONGODB_URI environment variable is not defined');
  process.exit(1);
}

/**
 * Test MongoDB connection and basic operations
 */
async function testDatabaseOperations() {
  console.log('🔍 Testing database operations...');
  
  let client;
  try {
    // Connect to MongoDB
    client = new MongoClient(MONGODB_URI, {
      connectTimeoutMS: 15000,
      socketTimeoutMS: 30000,
      maxPoolSize: 10,
      retryWrites: true,
      w: 'majority'
    });

    await client.connect();
    console.log('✅ Database connection successful');

    const db = client.db(MONGODB_DB);
    const usersCollection = db.collection('users');

    // Test basic operations
    const userCount = await usersCollection.countDocuments();
    console.log(`📊 Total users in database: ${userCount}`);

    // Test finding admin users
    const adminUsers = await usersCollection.find({ 
      role: { $in: ['admin', 'superadmin'] } 
    }).toArray();
    console.log(`👑 Admin users found: ${adminUsers.length}`);

    // Test user lookup by ID (simulate the deletion process)
    if (adminUsers.length > 0) {
      const testUserId = adminUsers[0]._id;
      const foundUser = await usersCollection.findOne({ _id: testUserId });
      console.log(`🔍 User lookup test: ${foundUser ? 'SUCCESS' : 'FAILED'}`);
    }

    return true;
  } catch (error) {
    console.error('❌ Database operation failed:', error.message);
    return false;
  } finally {
    if (client) {
      await client.close();
    }
  }
}

/**
 * Test environment variables
 */
function testEnvironmentVariables() {
  console.log('🔍 Testing environment variables...');
  
  const requiredVars = [
    'MONGODB_URI',
    'NEXT_PUBLIC_TMDB_API_KEY',
    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME'
  ];

  const missing = [];
  const present = [];

  for (const varName of requiredVars) {
    if (process.env[varName]) {
      present.push(varName);
    } else {
      missing.push(varName);
    }
  }

  console.log(`✅ Present variables: ${present.join(', ')}`);
  
  if (missing.length > 0) {
    console.log(`❌ Missing variables: ${missing.join(', ')}`);
    return false;
  }

  // Test environment-specific settings
  const environment = process.env.NODE_ENV || 'development';
  const isNetlify = process.env.NETLIFY === 'true';
  
  console.log(`🌍 Environment: ${environment}`);
  console.log(`☁️ Netlify: ${isNetlify ? 'YES' : 'NO'}`);
  
  if (isNetlify) {
    console.log('🔧 Netlify-specific settings detected');
  }

  return true;
}

/**
 * Test API endpoint accessibility
 */
async function testAPIEndpoints() {
  console.log('🔍 Testing API endpoint structure...');
  
  const fs = require('fs');
  const path = require('path');
  
  try {
    // Check if the user deletion route exists
    const routePath = path.join(process.cwd(), 'src', 'app', 'api', 'admin', 'users', '[id]', 'route.ts');
    
    if (fs.existsSync(routePath)) {
      console.log('✅ User deletion route file exists');
      
      // Read the file and check for key functions
      const routeContent = fs.readFileSync(routePath, 'utf8');
      
      const checks = [
        { name: 'DELETE function', pattern: /export async function DELETE/ },
        { name: 'verifyAdmin call', pattern: /verifyAdmin\(/ },
        { name: 'User.findByIdAndDelete', pattern: /User\.findByIdAndDelete/ },
        { name: 'Error handling', pattern: /catch\s*\(/ },
        { name: 'Enhanced error handling', pattern: /createErrorResponse/ }
      ];

      for (const check of checks) {
        if (check.pattern.test(routeContent)) {
          console.log(`✅ ${check.name}: Found`);
        } else {
          console.log(`❌ ${check.name}: Missing`);
        }
      }
      
      return true;
    } else {
      console.log('❌ User deletion route file not found');
      return false;
    }
  } catch (error) {
    console.error('❌ API endpoint test failed:', error.message);
    return false;
  }
}

/**
 * Test configuration files
 */
function testConfigurationFiles() {
  console.log('🔍 Testing configuration files...');
  
  const fs = require('fs');
  const path = require('path');
  
  try {
    // Check netlify.toml
    const netlifyConfigPath = path.join(process.cwd(), 'netlify.toml');
    if (fs.existsSync(netlifyConfigPath)) {
      console.log('✅ netlify.toml exists');
      
      const netlifyConfig = fs.readFileSync(netlifyConfigPath, 'utf8');
      
      // Check for important configurations
      if (netlifyConfig.includes('api/admin/users')) {
        console.log('✅ User admin endpoint configuration found');
      } else {
        console.log('⚠️ User admin endpoint configuration not found');
      }
      
      if (netlifyConfig.includes('FUNCTION_TIME_LIMIT')) {
        console.log('✅ Function timeout configuration found');
      } else {
        console.log('⚠️ Function timeout configuration not found');
      }
    } else {
      console.log('❌ netlify.toml not found');
    }

    // Check next.config.js
    const nextConfigPath = path.join(process.cwd(), 'next.config.js');
    if (fs.existsSync(nextConfigPath)) {
      console.log('✅ next.config.js exists');
    } else {
      console.log('❌ next.config.js not found');
    }

    return true;
  } catch (error) {
    console.error('❌ Configuration test failed:', error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Starting User Deletion System Tests\n');
  
  const tests = [
    { name: 'Environment Variables', fn: testEnvironmentVariables },
    { name: 'Database Operations', fn: testDatabaseOperations },
    { name: 'API Endpoints', fn: testAPIEndpoints },
    { name: 'Configuration Files', fn: testConfigurationFiles }
  ];

  let passedTests = 0;
  
  for (const test of tests) {
    console.log(`\n📋 Running ${test.name} test...`);
    try {
      const result = await test.fn();
      if (result) {
        console.log(`✅ ${test.name} test: PASSED`);
        passedTests++;
      } else {
        console.log(`❌ ${test.name} test: FAILED`);
      }
    } catch (error) {
      console.log(`❌ ${test.name} test: ERROR - ${error.message}`);
    }
  }

  console.log(`\n📊 Test Results: ${passedTests}/${tests.length} tests passed`);
  
  if (passedTests === tests.length) {
    console.log('🎉 All tests passed! User deletion system should work correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }
}

// Run the tests
runTests().catch(console.error);
