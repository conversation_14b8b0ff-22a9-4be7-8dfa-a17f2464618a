import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';

/**
 * Get the admin user ID from cookies or query parameters
 * and verify that the user has admin privileges
 */
export async function getAdminUserId(request: NextRequest): Promise<{
  userId: string | null;
  isAuthorized: boolean;
  error?: NextResponse;
}> {
  try {
    // In development mode, still get the real user ID but skip admin verification
    if ((process.env.NODE_ENV as string) === 'development') {

      // Get the user ID from the cookie or query parameter
      const { searchParams } = new URL(request.url);
      let userId = request.cookies.get('userId')?.value;

      // If no userId in cookies, try query string
      if (!userId) {
        userId = searchParams.get('userId') || '';
      }

      // If still no userId, try to get from session
      if (!userId) {
        try {
          const body = await request.clone().json();
          userId = body.userId;
        } catch (e) {
          // Ignore JSON parsing errors
        }
      }

      if (userId) {
        return {
          userId: userId,
          isAuthorized: true
        };
      }

      // Fallback to a valid ObjectId for development
      return {
        userId: '507f1f77bcf86cd799439011', // Valid ObjectId format
        isAuthorized: true
      };
    }

    // Check for development bypass header
    const bypassHeader = request.headers.get('x-admin-bypass');
    if ((process.env.NODE_ENV as string) === 'development' && bypassHeader === 'true') {
      return {
        userId: 'dev-admin-id',
        isAuthorized: true
      };
    }

    // Get the user ID from the cookie or query parameter
    const { searchParams } = new URL(request.url);
    let userId = request.cookies.get('userId')?.value;

    // If no userId in cookies, try query string
    if (!userId) {
      userId = searchParams.get('userId') || '';
    }

    if (!userId) {
      return {
        userId: null,
        isAuthorized: false,
        error: NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      };
    }

    // In development mode, allow access without database check
    if ((process.env.NODE_ENV as string) === 'development') {
      return {
        userId: userId || 'dev-admin-id',
        isAuthorized: true
      };
    }

    // Connect to database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId).select('role').lean();

    if (!user) {
      // In development mode, allow access even if user not found
      if ((process.env.NODE_ENV as string) === 'development') {
        return {
          userId,
          isAuthorized: true
        };
      }

      return {
        userId,
        isAuthorized: false,
        error: NextResponse.json({ error: 'User not found' }, { status: 404 })
      };
    }

    // Check if user has admin role
    const isUserAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isUserAdmin) {
      // In development mode, allow access even if not admin
      if ((process.env.NODE_ENV as string) === 'development') {
        return {
          userId,
          isAuthorized: true
        };
      }

      return {
        userId,
        isAuthorized: false,
        error: NextResponse.json({ error: 'Forbidden: You do not have permission to access this resource.' }, { status: 403 })
      };
    }

    // User is authorized
    return { userId, isAuthorized: true };
  } catch (error) {
    console.error('Error in admin authentication:', error);

    // In development mode, allow access even if there's an error
    if ((process.env.NODE_ENV as string) === 'development') {
      return {
        userId: 'dev-admin-id',
        isAuthorized: true
      };
    }

    return {
      userId: null,
      isAuthorized: false,
      error: NextResponse.json(
        { error: 'Server error during authorization', message: error instanceof Error ? error.message : 'Unknown error' },
        { status: 500 }
      )
    };
  }
}

/**
 * Verify if a user has admin privileges from a request
 * @param request The Next.js request object
 * @returns An object with authentication details and user information
 */
export async function verifyAdmin(request: NextRequest) {
  // Get the userId from cookies
  const userId = request.cookies.get('userId')?.value;
  
  try {
    const environment = process.env.NODE_ENV;
    const allCookies = request.cookies.getAll();

    // In development mode, be more permissive but still try to use real data
    if (environment === 'development') {

      // If we have a real userId, try to use it but don't fail if user doesn't exist
      if (userId) {
        try {
          await ensureMongooseConnection();
          const { default: User } = await import('@/models/User');
          const user = await User.findById(userId).select('email role').lean();
          
          if (user) {
            const isAdmin = user.role === 'admin' || user.role === 'superadmin';
            
            if (isAdmin) {
              return {
                isAuthorized: true,
                userId,
                user
              };
            } else {
              return {
                isAuthorized: true,
                userId,
                user: { ...user, role: 'admin' } // Override role in development
              };
            }
          }
        } catch (dbError) {
          // Database error in development, continue with fallback
        }
      }

      // Fallback for development
      return {
        isAuthorized: true,
        userId: userId || '507f1f77bcf86cd799439011',
        user: {
          email: '<EMAIL>',
          role: 'admin'
        }
      };
    }

    // Production mode - strict authentication required
    console.log('verifyAdmin: Production mode - strict authentication required');

    // Check if the user is authenticated
    if (!userId) {
      console.log('verifyAdmin: No userId found in cookies - authentication failed');
      return null;
    }

    console.log(`verifyAdmin: Attempting to validate userId: ${userId}`);

    // Connect to database
    try {
      await ensureMongooseConnection();
      console.log('verifyAdmin: Database connection successful');
    } catch (dbError) {
      console.error('verifyAdmin: Database connection failed:', dbError);
      return null;
    }

    // Import User model dynamically to avoid circular dependencies
    const { default: User } = await import('@/models/User');

    // Find user by ID
    const user = await User.findById(userId).select('email role name').lean();

    if (!user) {
      console.log(`verifyAdmin: User not found in database for ID ${userId}`);
      return null;
    }

    console.log(`verifyAdmin: User found - email: ${user.email}, role: ${user.role}`);

    // Check if user has admin role
    const isAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isAdmin) {
      console.log(`verifyAdmin: User ${userId} does not have admin role (current role: ${user.role})`);
      return null;
    }

    console.log(`verifyAdmin: Authentication successful for admin user ${user.email}`);
    return {
      isAuthorized: true,
      userId,
      user
    };
  } catch (error) {
    console.error('verifyAdmin: Unexpected error during authentication:', error);

    // Enhanced error logging for production debugging
    if (process.env.NODE_ENV === 'production') {
      console.error('verifyAdmin: Production error details:', {
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        userId: userId || 'undefined',
        environment: process.env.NODE_ENV,
        netlify: process.env.NETLIFY,
        mongodbUri: process.env.MONGODB_URI ? 'defined' : 'undefined'
      });
    }

    // In development mode, still allow access if there's an error
    if (process.env.NODE_ENV === 'development') {
      console.log('verifyAdmin: Development mode - allowing access despite error');
      return {
        isAuthorized: true,
        userId: 'dev-admin-fallback',
        user: {
          email: '<EMAIL>',
          role: 'admin'
        }
      };
    }

    return null;
  }
}
