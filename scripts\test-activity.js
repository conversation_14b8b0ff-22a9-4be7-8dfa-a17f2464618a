const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

// Define UserActivity schema
const UserActivitySchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: ['auth', 'content', 'profile', 'admin', 'payment', 'error', 'system'],
    index: true
  },
  action: {
    type: String,
    required: true,
    index: true
  },
  details: {
    type: String,
    required: true
  },
  ipAddress: {
    type: String
  },
  userAgent: {
    type: String
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true
});

const UserActivity = mongoose.models.UserActivity || mongoose.model('UserActivity', UserActivitySchema);

// Define User schema
const UserSchema = new mongoose.Schema({
  name: String,
  email: String,
  role: String
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

async function createTestActivity() {
  try {
    await connectDB();

    // Get some users from the database
    const users = await User.find({}).limit(5);
    
    if (users.length === 0) {
      console.log('No users found in database. Please create some users first.');
      return;
    }

    console.log(`Found ${users.length} users`);

    // Clear existing test activities
    await UserActivity.deleteMany({
      userAgent: 'Test Script'
    });

    // Create test activities for the last 7 days
    const activities = [];
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return date;
    });

    users.forEach((user, userIndex) => {
      last7Days.forEach((date, dayIndex) => {
        // Add login activity for each user on most days
        if (Math.random() > 0.2) {
          activities.push({
            userId: user._id,
            type: 'auth',
            action: 'login',
            details: 'User logged in successfully',
            timestamp: new Date(date.getTime() + Math.random() * 24 * 60 * 60 * 1000),
            ipAddress: '127.0.0.1',
            userAgent: 'Test Script',
            metadata: { test: true }
          });
        }

        // Add signup activity for first user on first day
        if (userIndex === 0 && dayIndex === 0) {
          activities.push({
            userId: user._id,
            type: 'auth',
            action: 'signup',
            details: 'User registered successfully',
            timestamp: new Date(date.getTime() + Math.random() * 24 * 60 * 60 * 1000),
            ipAddress: '127.0.0.1',
            userAgent: 'Test Script',
            metadata: { test: true }
          });
        }

        // Add some content activities
        if (Math.random() > 0.5) {
          activities.push({
            userId: user._id,
            type: 'content',
            action: 'view',
            details: 'User viewed content',
            timestamp: new Date(date.getTime() + Math.random() * 24 * 60 * 60 * 1000),
            ipAddress: '127.0.0.1',
            userAgent: 'Test Script',
            metadata: { test: true }
          });
        }
      });
    });

    // Insert all activities
    await UserActivity.insertMany(activities);
    console.log(`Created ${activities.length} test activities`);

    // Count activities by type
    const authCount = await UserActivity.countDocuments({ type: 'auth' });
    const loginCount = await UserActivity.countDocuments({ type: 'auth', action: 'login' });
    const signupCount = await UserActivity.countDocuments({ type: 'auth', action: 'signup' });

    console.log(`Total auth activities: ${authCount}`);
    console.log(`Login activities: ${loginCount}`);
    console.log(`Signup activities: ${signupCount}`);

    console.log('Test activity data created successfully!');
  } catch (error) {
    console.error('Error creating test activity:', error);
  } finally {
    await mongoose.disconnect();
  }
}

createTestActivity();
